<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件智能预览系统</title>
    <!-- 添加Markdown渲染库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <!-- 添加GitHub风格的Markdown样式 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/github-markdown-css/github-markdown.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f5f7fa;
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: white;
            padding: 24px 30px;
            border-bottom: 1px solid #edf2f7;
        }

        .header h1 {
            font-size: 1.75rem;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2d3748;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .header h1::before {
            font-size: 1.5em;
        }

        .header p {
            font-size: 1rem;
            color: #718096;
        }

        .main-content {
            padding: 24px 30px;
        }

        .file-list-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1rem;
            color: #4a5568;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-bottom: 16px;
            border-bottom: 1px solid #edf2f7;
        }

        .upload-btn {
            background: #0066ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s ease;
        }

        .upload-btn:hover {
            background: #0052cc;
        }

        .upload-btn::before {
            content: "⬆️";
        }

        .file-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }

        .file-table th {
            text-align: left;
            padding: 12px 16px;
            font-size: 14px;
            font-weight: 500;
            color: #718096;
            background: #f8fafc;
            border-bottom: 1px solid #edf2f7;
        }

        .file-table td {
            padding: 16px;
            font-size: 14px;
            border-bottom: 1px solid #edf2f7;
            color: #4a5568;
        }

        .file-table tr:hover {
            background: #f8fafc;
        }

        .file-name-cell {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .file-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            font-size: 18px;
        }

        .file-icon.image { background: #ebf8ff; color: #0066ff; }
        .file-icon.pdf { background: #fff5f5; color: #e53e3e; }
        .file-icon.text { background: #f0fff4; color: #38a169; }
        .file-icon.other { background: #f7fafc; color: #718096; }

        .file-name {
            font-weight: 500;
            color: #2d3748;
        }

        .file-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
            text-decoration: none;
            color: #4a5568;
            background: transparent;
        }

        .action-btn:hover {
            background: #edf2f7;
        }

        .action-btn.preview {
            color: #0066ff;
        }

        .action-btn.download {
            color: #38a169;
        }

        .action-btn.delete {
            color: #e53e3e;
        }

        .action-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            background: #f3f4f6;
            color: #9ca3af;
        }

        .action-btn.disabled:hover {
            background: #f3f4f6;
            transform: none;
        }

        /* Toast提示框样式 */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 24px;
            border-radius: 8px;
            color: white;
            font-size: 14px;
            font-weight: 500;
            opacity: 0;
            transform: translateY(-20px);
            transition: all 0.3s ease;
            z-index: 2000;
        }

        .toast.show {
            opacity: 1;
            transform: translateY(0);
        }

        .toast-success {
            background: #38a169;
            box-shadow: 0 4px 12px rgba(56, 161, 105, 0.2);
        }

        .toast-error {
            background: #e53e3e;
            box-shadow: 0 4px 12px rgba(229, 62, 62, 0.2);
        }

        /* 上传按钮禁用状态 */
        .upload-btn:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            opacity: 0.7;
        }

        /* 预览浮层样式 */
        .preview-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
            display: none;
            overflow: hidden;
        }

        .preview-modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.85);
            backdrop-filter: blur(4px);
        }

        .preview-modal-close {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            font-size: 28px;
            color: #fff;
            cursor: pointer;
            padding: 0;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.3s ease;
            z-index: 1002;
        }

        .preview-modal-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: rotate(90deg);
        }

        /* 预览控制按钮样式 */
        .preview-controls {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
            z-index: 1002;
            background: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 50px;
            backdrop-filter: blur(4px);
        }

        .preview-control-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            transition: all 0.3s ease;
        }

        .preview-control-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .preview-control-btn svg {
            width: 24px;
            height: 24px;
            transition: transform 0.3s ease;
        }

        .preview-control-btn:hover svg {
            transform: scale(1.1);
        }

        .preview-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1001;
        }

        .preview-placeholder {
            text-align: center;
            color: #fff;
            padding: 20px;
            background: transparent;
        }

        .preview-placeholder .icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.8;
        }

        .loading {
            display: none;
            text-align: center;
            color: #fff;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 20px;
            background: transparent;
            z-index: 1002;
        }

        .preview-content {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding: 40px;
        }

        .preview-content img {
            max-width: 95%;
            max-height: 95vh;
            object-fit: contain;
            border-radius: 8px;
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
            cursor: grab;
            transform-origin: center center;
            transition: transform 0.1s ease-out;
            will-change: transform;
            user-select: none;
            -webkit-user-drag: none;
        }

        .preview-content img:active {
            cursor: grabbing;
        }

        /* 添加图片加载动画 */
        @keyframes imgFadeIn {
            from { 
                opacity: 0; 
                transform: scale(0.98) translate(0, 0); 
            }
            to { 
                opacity: 1; 
                transform: scale(1) translate(0, 0); 
            }
        }

        .preview-content img {
            animation: imgFadeIn 0.3s ease;
        }

        /* 缩放提示 */
        .zoom-hint {
            position: fixed;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.5);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1002;
        }

        .zoom-hint.show {
            opacity: 1;
        }

        /* 添加动画效果 */
        .preview-modal[data-visible="true"] {
            display: block;
            animation: modalFadeIn 0.3s ease;
        }

        .preview-modal[data-visible="true"] .preview-modal-content {
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translate(-50%, -45%);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%);
            }
        }

        .preview-content iframe {
            width: 100%;
            height: 600px;
            border: none;
            border-radius: 8px;
        }

        .preview-content pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            overflow: auto;
            max-height: 600px;
            font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
            line-height: 1.5;
        }

        /* Markdown预览样式 */
        .markdown-body {
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: auto;
            max-height: 80vh;
        }

        .markdown-body pre {
            background: #f6f8fa;
            border-radius: 6px;
            padding: 16px;
            overflow: auto;
        }

        .markdown-body code {
            background: #f6f8fa;
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-size: 85%;
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
        }

        .error-message {
            color: #e74c3c;
            text-align: center;
            padding: 20px;
            background: #fee;
            border-radius: 8px;
            margin: 20px 0;
        }

        /* 分页样式 */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 30px 0;
            padding: 20px;
            background: white;
            border-radius: 12px;
            border: 1px solid #e0e6ed;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .pagination-controls select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: white;
            color: #333;
            font-size: 14px;
        }

        .pagination-buttons {
            display: flex;
            gap: 5px;
        }

        .pagination-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            color: #333;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            min-width: 40px;
            text-align: center;
        }

        .pagination-btn:hover:not(:disabled) {
            background: #f8f9fa;
            border-color: #4facfe;
        }

        .pagination-btn:disabled {
            background: #f8f9fa;
            color: #ccc;
            cursor: not-allowed;
        }

        .pagination-btn.active {
            background: #4facfe;
            color: white;
            border-color: #4facfe;
        }

        .pagination-btn.ellipsis {
            border: none;
            background: none;
            cursor: default;
        }

        .pagination-btn.ellipsis:hover {
            background: none;
            border: none;
        }

        @media (max-width: 768px) {
            .file-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>文件智能预览系统</h1>
            <p>支持图片、PDF、文本文件在线预览，其他文件自动下载</p>
        </div>

        <div class="main-content">
            <!-- 文件列表区域 -->
            <div class="file-list-section">
                <div class="section-title">
                    <span>附件</span>
                    <button class="upload-btn" onclick="document.getElementById('file-input').click()">上传附件</button>
                    <input type="file" id="file-input" style="display: none;" onchange="handleFileUpload(this.files)">
                </div>
                <table class="file-table">
                    <thead>
                        <tr>
                            <th>文件名称</th>
                            <th>大小</th>
                            <th>上传人</th>
                            <th>上传时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="file-list">
                        <!-- 文件列表将动态插入这里 -->
                    </tbody>
                </table>
                
                <!-- 分页控件 -->
                <div id="pagination-container" class="pagination-container" style="display: none;">
                    <div class="pagination-info">
                        <span id="pagination-info-text">显示第 1-10 项，共 0 项文件</span>
                    </div>
                    <div class="pagination-controls">
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <label for="page-size-select">每页显示:</label>
                            <select id="page-size-select" onchange="changePageSize(this.value)">
                                <option value="6">6 项</option>
                                <option value="12" selected>12 项</option>
                                <option value="24">24 项</option>
                                <option value="48">48 项</option>
                            </select>
                        </div>
                        <div class="pagination-buttons" id="pagination-buttons">
                            <!-- 分页按钮将动态生成 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 预览浮层 -->
            <div id="preview-modal" class="preview-modal" style="display: none;">
                <div class="preview-modal-overlay"></div>
                <button class="preview-modal-close" onclick="closePreview()">×</button>
                <div class="preview-controls">
                    <button class="preview-control-btn" onclick="zoomImage(1.2)" title="放大">
                        <svg viewBox="0 0 24 24" width="24" height="24">
                            <path fill="currentColor" d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                        </svg>
                    </button>
                    <button class="preview-control-btn" onclick="zoomImage(0.8)" title="缩小">
                        <svg viewBox="0 0 24 24" width="24" height="24">
                            <path fill="currentColor" d="M19 13H5v-2h14v2z"/>
                        </svg>
                    </button>
                    <button class="preview-control-btn" onclick="resetZoom()" title="重置">
                        <svg viewBox="0 0 24 24" width="24" height="24">
                            <path fill="currentColor" d="M12 5V1L7 6l5 5V7c3.31 0 6 2.69 6 6s-2.69 6-6 6-6-2.69-6-6H4c0 4.42 3.58 8 8 8s8-3.58 8-8-3.58-8-8-8z"/>
                        </svg>
                    </button>
                </div>
                <div id="preview-container" class="preview-container">
                    <div class="preview-placeholder">
                        <div class="icon">👁️</div>
                        <h3>选择文件开始预览</h3>
                        <p>点击上方文件的"预览"按钮查看文件内容</p>
                    </div>
                </div>
                <div id="loading" class="loading">
                    <h3>🔄 加载中...</h3>
                </div>
                <div id="zoom-hint" class="zoom-hint">
                    按住Ctrl+滚轮缩放 • 拖动移动图片
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentFiles = [];
        let currentPage = 1;
        let pageSize = 12;
        let totalFiles = 0;
        let currentZoom = 1;
        let isDragging = false;
        let startX, startY, translateX = 0, translateY = 0;

        // 页面加载时获取文件列表
        document.addEventListener('DOMContentLoaded', function() {
            loadFileList();
        });

        // 加载文件列表
        async function loadFileList() {
            try {
                const response = await fetch('/api/files');
                const data = await response.json();
                
                if (response.ok) {
                    currentFiles = data.files || [];
                    totalFiles = currentFiles.length;
                    currentPage = 1; // 重置到第一页
                    renderFileList();
                    renderPagination();
                } else {
                    showError('获取文件列表失败: ' + (data.error || '未知错误'));
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
            }
        }

        // 渲染文件列表
        function renderFileList() {
            const tbody = document.getElementById('file-list');
            
            if (currentFiles.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5" style="text-align: center; padding: 40px;">暂无文件</td></tr>';
                document.getElementById('pagination-container').style.display = 'none';
                return;
            }

            // 计算当前页显示的文件
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = Math.min(startIndex + pageSize, currentFiles.length);
            const currentPageFiles = currentFiles.slice(startIndex, endIndex);

            tbody.innerHTML = currentPageFiles.map((file, index) => {
                const globalIndex = startIndex + index; // 全局索引
                return `
                    <tr>
                        <td>
                            <div class="file-name-cell">
                                <div class="file-icon ${file.fileType}">
                                    ${getFileIcon(file.fileType)}
                                </div>
                                <span class="file-name">${escapeHtml(file.name)}</span>
                            </div>
                        </td>
                        <td>${formatFileSize(file.size)}</td>
                        <td>超级管理员</td>
                        <td>${file.lastModified}</td>
                        <td>
                             <div class="file-actions">
                                <button class="action-btn preview preview-btn ${!file.canPreview ? 'disabled' : ''}" 
                                    data-file-index="${globalIndex}" 
                                    ${!file.canPreview ? 'disabled' : ''}>
                                    预览
                                </button>
                                <a class="action-btn download" href="/api/preview/${encodeURIComponent(file.name).replace(/%2F/g, '/')}" download>下载</a>
                                <button class="action-btn delete">删除</button>
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');

            // 添加预览按钮事件监听器
            document.querySelectorAll('.preview-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const fileIndex = parseInt(this.getAttribute('data-file-index'));
                    const file = currentFiles[fileIndex];
                    if (file) {
                        previewFile(file.name);
                    }
                });
            });

            // 显示分页控件
            if (currentFiles.length > pageSize) {
                document.getElementById('pagination-container').style.display = 'flex';
            } else {
                document.getElementById('pagination-container').style.display = 'none';
            }
        }

        // 预览文件
        async function previewFile(filename) {
            console.log('开始预览文件:', filename);
            const modal = document.getElementById('preview-modal');
            const container = document.getElementById('preview-container');
            const loading = document.getElementById('loading');
            const modalTitle = document.getElementById('preview-modal-title');
            
            try {
                // 显示模态框和加载状态
                modal.setAttribute('data-visible', 'true');
                modal.style.display = 'block';
                container.innerHTML = ''; // 清空容器
                loading.style.display = 'block';
                console.log('显示加载状态');

                // 移除标题设置，因为我们不再使用标题

                // 获取文件信息 - 直接使用原始文件名，不进行编码
                const infoResponse = await fetch(`/api/info/${filename}`);
                const result = await infoResponse.json();
                console.log('文件信息:', result);
                
                if (!infoResponse.ok || result.code !== 200) {
                    throw new Error(result.error || '获取文件信息失败');
                }
                
                const fileInfo = result.data;

                // 根据文件类型渲染预览内容
                const previewUrl = `/api/preview/${filename}`;
                console.log('预览URL:', previewUrl);
                
                switch (fileInfo.fileType) {
                    case 'image':
                        // 创建预览容器
                        const previewDiv = document.createElement('div');
                        previewDiv.className = 'preview-content';
                        
                        // 创建图片元素
                        const img = document.createElement('img');
                        img.alt = filename;
                        
                        // 添加加载事件
                        img.onload = () => {
                            console.log('图片加载完成');
                            loading.style.display = 'none';
                            
                            // 显示缩放提示
                            const hint = document.getElementById('zoom-hint');
                            hint.classList.add('show');
                            setTimeout(() => {
                                hint.classList.remove('show');
                            }, 3000);
                        };
                        img.onerror = () => {
                            console.log('图片加载失败');
                            loading.style.display = 'none';
                            container.innerHTML = `
                                <div class="error-message">
                                    <h3>❌ 图片加载失败</h3>
                                    <p>无法加载图片: ${escapeHtml(filename)}</p>
                                </div>
                            `;
                        };
                        
                        // 设置图片源
                        img.src = previewUrl;
                        
                        // 添加到容器
                        previewDiv.appendChild(img);
                        container.appendChild(previewDiv);
                        break;
                        
                    case 'pdf':
                        const iframe = document.createElement('iframe');
                        iframe.src = previewUrl;
                        iframe.className = 'preview-content';
                        iframe.title = filename;
                        iframe.onload = () => {
                            console.log('PDF加载完成');
                            loading.style.display = 'none';
                        };
                        container.appendChild(iframe);
                        break;
                        
                    case 'text':
                        // 对于文本文件，需要获取内容并显示
                        const textResponse = await fetch(previewUrl);
                        const textResult = await textResponse.json();
                        
                        if (!textResponse.ok || textResult.code !== 200) {
                            throw new Error(textResult.error || '获取文本内容失败');
                        }
                        
                        // 检查文件扩展名
                        const ext = filename.toLowerCase().split('.').pop();
                        if (ext === 'md') {
                            // 如果是Markdown文件，使用marked库渲染
                            const markedContainer = document.createElement('div');
                            markedContainer.className = 'preview-content markdown-body';
                            markedContainer.innerHTML = marked.parse(textResult.data);
                            container.appendChild(markedContainer);
                        } else {
                            // 其他文本文件使用pre标签
                            const pre = document.createElement('pre');
                            pre.className = 'preview-content';
                            pre.textContent = textResult.data;
                            container.appendChild(pre);
                        }
                        loading.style.display = 'none';
                        break;
                        
                    default:
                        throw new Error('不支持预览此文件类型');
                }
                
            } catch (error) {
                console.error('预览错误:', error);
                container.innerHTML = `
                    <div class="error-message">
                        <h3>❌ 预览失败</h3>
                        <p>${error.message}</p>
                    </div>
                `;
                loading.style.display = 'none';
            }
        }

        // 关闭预览
        function closePreview() {
            const modal = document.getElementById('preview-modal');
            modal.setAttribute('data-visible', 'false');
            modal.style.display = 'none';
            
            // 重置缩放和位置
            currentZoom = 1;
            translateX = 0;
            translateY = 0;
            
            // 清空预览内容
            const container = document.getElementById('preview-container');
            container.innerHTML = `
                <div class="preview-placeholder">
                    <div class="icon">👁️</div>
                    <h3>选择文件开始预览</h3>
                    <p>点击上方文件的"预览"按钮查看文件内容</p>
                </div>
            `;
        }

        // 缩放图片
        function zoomImage(factor) {
            const img = document.querySelector('.preview-content img');
            if (!img) return;

            currentZoom *= factor;
            // 限制缩放范围
            currentZoom = Math.min(Math.max(0.1, currentZoom), 5);
            
            updateImageTransform(img);
        }

        // 重置缩放
        function resetZoom() {
            const img = document.querySelector('.preview-content img');
            if (!img) return;

            currentZoom = 1;
            translateX = 0;
            translateY = 0;
            
            updateImageTransform(img);
        }

        // 更新图片变换
        function updateImageTransform(img) {
            img.style.transform = `translate(${translateX}px, ${translateY}px) scale(${currentZoom})`;
        }

        // 添加拖动功能
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.getElementById('preview-container');

            container.addEventListener('mousedown', startDragging);
            container.addEventListener('mousemove', drag);
            container.addEventListener('mouseup', stopDragging);
            container.addEventListener('mouseleave', stopDragging);
            container.addEventListener('wheel', handleWheel);
        });

        function startDragging(e) {
            const img = document.querySelector('.preview-content img');
            if (!img || e.button !== 0) return;

            isDragging = true;
            startX = e.clientX - translateX;
            startY = e.clientY - translateY;
            img.style.cursor = 'grabbing';
        }

        function drag(e) {
            const img = document.querySelector('.preview-content img');
            if (!isDragging || !img) return;

            e.preventDefault();
            translateX = e.clientX - startX;
            translateY = e.clientY - startY;
            
            updateImageTransform(img);
        }

        function stopDragging() {
            const img = document.querySelector('.preview-content img');
            if (!img) return;

            isDragging = false;
            img.style.cursor = 'grab';
        }

        function handleWheel(e) {
            const img = document.querySelector('.preview-content img');
            if (!img || !e.ctrlKey) return;

            e.preventDefault();
            const factor = e.deltaY > 0 ? 0.9 : 1.1;
            zoomImage(factor);
        }

        // 添加ESC键关闭预览
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closePreview();
            }
        });

        // 点击遮罩层关闭预览
        document.querySelector('.preview-modal-overlay').addEventListener('click', closePreview);

        // 获取文件图标
        function getFileIcon(fileType) {
            switch (fileType) {
                case 'image': return '🖼️';
                case 'pdf': return '📄';
                case 'text': return '📝';
                default: return '📁';
            }
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 显示错误信息
        function showError(message) {
            const tbody = document.getElementById('file-list');
            tbody.innerHTML = `
                <tr>
                    <td colspan="5">
                        <div class="error-message">
                            <h3>❌ 错误</h3>
                            <p>${message}</p>
                        </div>
                    </td>
                </tr>
            `;
        }

        // 处理文件上传
        async function handleFileUpload(files) {
            if (!files || files.length === 0) return;

            const file = files[0];
            const formData = new FormData();
            formData.append('file', file);

            try {
                // 显示上传中状态
                const uploadBtn = document.querySelector('.upload-btn');
                const originalText = uploadBtn.textContent;
                uploadBtn.textContent = '上传中...';
                uploadBtn.disabled = true;

                const response = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (!response.ok) {
                    throw new Error(result.error || '上传失败');
                }

                // 刷新文件列表
                await loadFileList();

                // 显示成功提示
                showToast('上传成功');

            } catch (error) {
                console.error('上传失败:', error);
                showToast('上传失败: ' + error.message, 'error');
            } finally {
                // 恢复按钮状态
                const uploadBtn = document.querySelector('.upload-btn');
                uploadBtn.textContent = '上传附件';
                uploadBtn.disabled = false;
                // 清空文件输入框
                document.getElementById('file-input').value = '';
            }
        }

        // 显示提示信息
        function showToast(message, type = 'success') {
            // 创建提示元素
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.textContent = message;
            
            // 添加到页面
            document.body.appendChild(toast);
            
            // 显示动画
            setTimeout(() => toast.classList.add('show'), 10);
            
            // 自动隐藏
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }

        // 渲染分页控件
        function renderPagination() {
            const totalPages = Math.ceil(totalFiles / pageSize);
            const paginationButtons = document.getElementById('pagination-buttons');
            const paginationInfo = document.getElementById('pagination-info-text');
            
            // 更新分页信息
            const startItem = totalFiles === 0 ? 0 : (currentPage - 1) * pageSize + 1;
            const endItem = Math.min(currentPage * pageSize, totalFiles);
            paginationInfo.textContent = `显示第 ${startItem}-${endItem} 项，共 ${totalFiles} 项文件`;
            
            if (totalPages <= 1) {
                paginationButtons.innerHTML = '';
                return;
            }
            
            let buttons = [];
            
            // 上一页按钮
            buttons.push(`
                <button class="pagination-btn" ${currentPage === 1 ? 'disabled' : ''} onclick="goToPage(${currentPage - 1})">
                    ‹ 上一页
                </button>
            `);
            
            // 页码按钮逻辑
            let startPage = Math.max(1, currentPage - 2);
            let endPage = Math.min(totalPages, currentPage + 2);
            
            // 确保显示5个页码（如果可能）
            if (endPage - startPage < 4) {
                if (startPage === 1) {
                    endPage = Math.min(totalPages, startPage + 4);
                } else {
                    startPage = Math.max(1, endPage - 4);
                }
            }
            
            // 第一页
            if (startPage > 1) {
                buttons.push(`<button class="pagination-btn" onclick="goToPage(1)">1</button>`);
                if (startPage > 2) {
                    buttons.push(`<button class="pagination-btn ellipsis">...</button>`);
                }
            }
            
            // 中间页码
            for (let i = startPage; i <= endPage; i++) {
                buttons.push(`
                    <button class="pagination-btn ${i === currentPage ? 'active' : ''}" onclick="goToPage(${i})">
                        ${i}
                    </button>
                `);
            }
            
            // 最后一页
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    buttons.push(`<button class="pagination-btn ellipsis">...</button>`);
                }
                buttons.push(`<button class="pagination-btn" onclick="goToPage(${totalPages})">${totalPages}</button>`);
            }
            
            // 下一页按钮
            buttons.push(`
                <button class="pagination-btn" ${currentPage === totalPages ? 'disabled' : ''} onclick="goToPage(${currentPage + 1})">
                    下一页 ›
                </button>
            `);
            
            paginationButtons.innerHTML = buttons.join('');
        }

        // 跳转到指定页
        function goToPage(page) {
            const totalPages = Math.ceil(totalFiles / pageSize);
            if (page < 1 || page > totalPages || page === currentPage) {
                return;
            }
            
            currentPage = page;
            renderFileList();
            renderPagination();
            
            // 滚动到文件列表顶部
            document.getElementById('file-grid').scrollIntoView({ 
                behavior: 'smooth', 
                block: 'start' 
            });
        }

        // 改变每页显示数量
        function changePageSize(newSize) {
            pageSize = parseInt(newSize);
            currentPage = 1; // 重置到第一页
            renderFileList();
            renderPagination();
        }
    </script>
</body>
</html>
