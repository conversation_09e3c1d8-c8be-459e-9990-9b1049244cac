package service

import (
	"context"
	"fmt"
	"io"
	"log"
	"strings"

	"file-preview/config"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
)

// MinIOClient MinIO客户端封装
type MinIOClient struct {
	client *minio.Client
	bucket string
}

// GetClient 获取MinIO客户端实例
func (mc *MinIOClient) GetClient() *minio.Client {
	return mc.client
}

// NewMinIOClient 创建MinIO客户端
func NewMinIOClient(cfg *config.StoreConfig) (*MinIOClient, error) {
	// 解析endpoint，确定是否使用HTTPS
	endpoint := cfg.Endpoint
	secure := false
	if strings.HasPrefix(endpoint, "https://") {
		endpoint = endpoint[8:]
		secure = true
	} else if strings.HasPrefix(endpoint, "http://") {
		endpoint = endpoint[7:]
	}

	// 初始化MinIO客户端
	client, err := minio.New(endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(cfg.AccessID, cfg.AccessKey, ""),
		Secure: secure,
	})
	if err != nil {
		return nil, fmt.Errorf("初始化MinIO客户端失败: %v", err)
	}

	// 检查bucket是否存在
	ctx := context.Background()
	exists, err := client.BucketExists(ctx, cfg.BucketName)
	if err != nil {
		return nil, fmt.Errorf("检查bucket存在性失败: %v", err)
	}
	if !exists {
		log.Printf("警告: bucket '%s' 不存在", cfg.BucketName)
	}

	return &MinIOClient{
		client: client,
		bucket: cfg.BucketName,
	}, nil
}

// GetObject 获取文件对象
func (mc *MinIOClient) GetObject(ctx context.Context, objectName string) (*minio.Object, error) {
	object, err := mc.client.GetObject(ctx, mc.bucket, objectName, minio.GetObjectOptions{})
	if err != nil {
		return nil, fmt.Errorf("获取文件对象失败: %v", err)
	}
	return object, nil
}

// GetObjectInfo 获取文件信息
func (mc *MinIOClient) GetObjectInfo(ctx context.Context, objectName string) (minio.ObjectInfo, error) {
	info, err := mc.client.StatObject(ctx, mc.bucket, objectName, minio.StatObjectOptions{})
	if err != nil {
		return minio.ObjectInfo{}, fmt.Errorf("获取文件信息失败: %v", err)
	}
	return info, nil
}

// GetObjectReader 获取文件读取器
func (mc *MinIOClient) GetObjectReader(ctx context.Context, objectName string) (io.ReadCloser, int64, error) {
	// 先获取文件信息
	info, err := mc.GetObjectInfo(ctx, objectName)
	if err != nil {
		return nil, 0, err
	}

	// 获取文件对象
	object, err := mc.GetObject(ctx, objectName)
	if err != nil {
		return nil, 0, err
	}

	return object, info.Size, nil
}

// ListObjects 列出所有对象
func (mc *MinIOClient) ListObjects(ctx context.Context, prefix string) ([]minio.ObjectInfo, error) {
	var objects []minio.ObjectInfo

	objectCh := mc.client.ListObjects(ctx, mc.bucket, minio.ListObjectsOptions{
		Prefix:    prefix,
		Recursive: true,
	})

	for object := range objectCh {
		if object.Err != nil {
			return nil, fmt.Errorf("列出对象失败: %v", object.Err)
		}
		objects = append(objects, object)
	}

	return objects, nil
}
