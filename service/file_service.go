package service

import (
	"context"
	"fmt"
	"io"
	"path/filepath"
	"strings"
)

// FileType 文件类型枚举
type FileType int

const (
	FileTypeImage FileType = iota
	FileTypePDF
	FileTypeText
	FileTypeOther
)

// String 返回文件类型的字符串表示
func (ft FileType) String() string {
	switch ft {
	case FileTypeImage:
		return "image"
	case FileTypePDF:
		return "pdf"
	case FileTypeText:
		return "text"
	default:
		return "other"
	}
}

// PreviewInfo 预览信息
type PreviewInfo struct {
	FileType           FileType
	ContentType        string
	ContentDisposition string
	ShouldDownload     bool
}

// FileService 文件服务接口
type FileService interface {
	GetPreviewInfo(filename string) PreviewInfo
	GetFile(ctx context.Context, filename string) (io.ReadCloser, int64, error)
}

// fileService 文件服务实现
type fileService struct {
	storage StorageClient
}

// StorageClient 存储客户端接口
type StorageClient interface {
	GetObjectReader(ctx context.Context, objectName string) (io.ReadCloser, int64, error)
}

// NewFileService 创建文件服务实例
func NewFileService(storage StorageClient) FileService {
	return &fileService{
		storage: storage,
	}
}

// GetPreviewInfo 获取文件预览信息
func (fs *fileService) GetPreviewInfo(filename string) PreviewInfo {
	fileType := fs.detectFileType(filename)
	contentType := fs.getContentType(filename, fileType)

	var contentDisposition string
	var shouldDownload bool

	switch fileType {
	case FileTypeImage, FileTypePDF, FileTypeText:
		// 浏览器内直接预览
		contentDisposition = fmt.Sprintf("inline; filename=\"%s\"", filename)
		shouldDownload = false
	default:
		// 触发下载
		contentDisposition = fmt.Sprintf("attachment; filename=\"%s\"", filename)
		shouldDownload = true
	}

	return PreviewInfo{
		FileType:           fileType,
		ContentType:        contentType,
		ContentDisposition: contentDisposition,
		ShouldDownload:     shouldDownload,
	}
}

// GetFile 获取文件内容
func (fs *fileService) GetFile(ctx context.Context, filename string) (io.ReadCloser, int64, error) {
	return fs.storage.GetObjectReader(ctx, filename)
}

// detectFileType 检测文件类型
func (fs *fileService) detectFileType(filename string) FileType {
	ext := strings.ToLower(filepath.Ext(filename))

	switch ext {
	case ".png", ".jpg", ".jpeg", ".gif", ".bmp", ".webp", ".svg":
		return FileTypeImage
	case ".pdf":
		return FileTypePDF
	case ".txt", ".log", ".md", ".json", ".xml", ".csv", ".yaml", ".yml":
		return FileTypeText
	default:
		return FileTypeOther
	}
}

// getContentType 获取Content-Type
func (fs *fileService) getContentType(filename string, fileType FileType) string {
	ext := strings.ToLower(filepath.Ext(filename))

	// 根据文件扩展名返回精确的MIME类型
	switch ext {
	// 图片类型
	case ".png":
		return "image/png"
	case ".jpg", ".jpeg":
		return "image/jpeg"
	case ".gif":
		return "image/gif"
	case ".bmp":
		return "image/bmp"
	case ".webp":
		return "image/webp"
	case ".svg":
		return "image/svg+xml"

	// PDF类型
	case ".pdf":
		return "application/pdf"

	// 文本类型
	case ".txt", ".log":
		return "text/plain; charset=utf-8"
	case ".md":
		return "text/markdown; charset=utf-8"
	case ".json":
		return "application/json; charset=utf-8"
	case ".xml":
		return "application/xml; charset=utf-8"
	case ".csv":
		return "text/csv; charset=utf-8"
	case ".yaml", ".yml":
		return "application/x-yaml; charset=utf-8"

	// 其他类型
	default:
		return "application/octet-stream"
	}
}
