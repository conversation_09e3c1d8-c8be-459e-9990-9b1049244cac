package handler

import (
	"context"
	"fmt"
	"log"
	"mime/multipart"
	"net/http"
	"path/filepath"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/minio/minio-go/v7"
)

// UploadHandler 文件上传处理器
type UploadHandler struct {
	minioClient *minio.Client
	bucketName  string
}

// NewUploadHandler 创建上传处理器
func NewUploadHandler(minioClient *minio.Client, bucketName string) *UploadHandler {
	return &UploadHandler{
		minioClient: minioClient,
		bucketName:  bucketName,
	}
}

// UploadFile 处理文件上传
func (h *UploadHandler) UploadFile(c *gin.Context) {
	// 获取上传的文件
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		log.Printf("获取上传文件失败: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "请选择要上传的文件"})
		return
	}
	defer file.Close()

	// 生成文件名
	filename := generateFilename(header.Filename)

	// 创建上传上下文
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 获取文件大小
	size := header.Size

	// 上传到MinIO
	_, err = h.minioClient.PutObject(ctx, h.bucketName, filename, file, size, minio.PutObjectOptions{
		ContentType: header.Header.Get("Content-Type"),
	})

	if err != nil {
		log.Printf("上传文件到MinIO失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "文件上传失败"})
		return
	}

	// 返回成功信息
	c.JSON(http.StatusOK, gin.H{
		"message": "文件上传成功",
		"file": gin.H{
			"name":         filename,
			"size":         size,
			"contentType":  header.Header.Get("Content-Type"),
			"lastModified": time.Now().Format("2006-01-02 15:04:05"),
		},
	})
}

// generateFilename 生成文件名
func generateFilename(originalName string) string {
	// 获取文件扩展名
	ext := filepath.Ext(originalName)
	// 生成时间戳格式的文件名
	timestamp := time.Now().Format("20060102150405")
	// 生成随机字符串
	randomStr := generateRandomString(6)
	// 组合新文件名: 时间戳_随机字符串.扩展名
	return fmt.Sprintf("%s_%s%s", timestamp, randomStr, ext)
}

// generateRandomString 生成随机字符串
func generateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, length)
	for i := range result {
		result[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(result)
}


