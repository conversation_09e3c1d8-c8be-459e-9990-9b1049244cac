package handler

import (
	"context"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"file-preview-service/service"
	"file-preview-service/storage"

	"github.com/gin-gonic/gin"
)

// FileHandler 文件处理器
type FileHandler struct {
	fileService service.FileService
	minioClient *storage.MinIOClient
}

// NewFileHandler 创建文件处理器
func NewFileHandler(fileService service.FileService, minioClient *storage.MinIOClient) *FileHandler {
	return &FileHandler{
		fileService: fileService,
		minioClient: minioClient,
	}
}

// PreviewFile 文件预览处理器
func (h *FileHandler) PreviewFile(c *gin.Context) {
	filename := c.Param("filename")
	if filename == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "文件名不能为空"})
		return
	}

	// URL解码文件名，保留斜杠
	decodedFilename := strings.ReplaceAll(filename, "%2F", "/")
	decodedFilename, err := url.QueryUnescape(decodedFilename)
	if err != nil {
		log.Printf("URL解码失败: %v", err)
		decodedFilename = filename // 使用原始文件名
	}

	// 获取预览信息
	previewInfo := h.fileService.GetPreviewInfo(decodedFilename)

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 获取文件内容
	reader, size, err := h.fileService.GetFile(ctx, decodedFilename)
	if err != nil {
		log.Printf("获取文件失败: %v", err)
		c.JSON(http.StatusNotFound, gin.H{
			"code":  404,
			"error": "文件不存在或获取失败",
		})
		return
	}
	defer reader.Close()

	// 对于文本文件，返回JSON格式的响应
	if previewInfo.FileType == service.FileTypeText {
		content, err := io.ReadAll(reader)
		if err != nil {
			log.Printf("读取文件内容失败: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":  500,
				"error": "读取文件内容失败",
			})
			return
		}
		c.JSON(http.StatusOK, gin.H{
			"code": 200,
			"data": string(content),
		})
		return
	}

	// 对于其他类型文件，设置响应头并直接流式传输
	c.Header("Content-Type", previewInfo.ContentType)
	c.Header("Content-Disposition", previewInfo.ContentDisposition)
	c.Header("Content-Length", fmt.Sprintf("%d", size))
	c.Header("Cache-Control", "public, max-age=3600") // 缓存1小时
	_, err = io.Copy(c.Writer, reader)
	if err != nil {
		log.Printf("传输文件内容失败: %v", err)
		// 此时已经开始传输，无法返回JSON错误
		return
	}
}

// ListFiles 列出文件列表
func (h *FileHandler) ListFiles(c *gin.Context) {
	prefix := c.Query("prefix")

	// 分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "100"))

	// 限制分页参数
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 100
	}

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	objects, err := h.minioClient.ListObjects(ctx, prefix)
	if err != nil {
		log.Printf("列出文件失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取文件列表失败"})
		return
	}

	// 构建文件信息列表
	type FileInfo struct {
		Name         string `json:"name"`
		Size         int64  `json:"size"`
		LastModified string `json:"lastModified"`
		FileType     string `json:"fileType"`
		CanPreview   bool   `json:"canPreview"`
	}

	var allFiles []FileInfo
	for _, obj := range objects {
		previewInfo := h.fileService.GetPreviewInfo(obj.Key)
		allFiles = append(allFiles, FileInfo{
			Name:         obj.Key,
			Size:         obj.Size,
			LastModified: obj.LastModified.Format("2006-01-02 15:04:05"),
			FileType:     previewInfo.FileType.String(),
			CanPreview:   !previewInfo.ShouldDownload,
		})
	}

	// 计算分页
	total := len(allFiles)
	totalPages := (total + pageSize - 1) / pageSize

	// 计算分页范围
	startIndex := (page - 1) * pageSize
	endIndex := startIndex + pageSize
	if endIndex > total {
		endIndex = total
	}

	var files []FileInfo
	if startIndex < total {
		files = allFiles[startIndex:endIndex]
	}

	c.JSON(http.StatusOK, gin.H{
		"files":      files,
		"total":      total,
		"page":       page,
		"pageSize":   pageSize,
		"totalPages": totalPages,
		"hasNext":    page < totalPages,
		"hasPrev":    page > 1,
	})
}

// GetFileInfo 获取文件信息
func (h *FileHandler) GetFileInfo(c *gin.Context) {
	filename := c.Param("filename")
	if filename == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "文件名不能为空"})
		return
	}

	// 直接使用文件名，不进行URL解码
	decodedFilename := filename

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 获取文件信息
	info, err := h.minioClient.GetObjectInfo(ctx, decodedFilename)
	if err != nil {
		log.Printf("获取文件信息失败: %v", err)
		c.JSON(http.StatusNotFound, gin.H{
			"error": "文件不存在",
			"code":  404,
		})
		return
	}

	// 获取预览信息
	previewInfo := h.fileService.GetPreviewInfo(decodedFilename)

	// 构建标准响应格式
	response := gin.H{
		"code": 200,
		"data": gin.H{
			"name":         info.Key,
			"size":         info.Size,
			"lastModified": info.LastModified.Format("2006-01-02 15:04:05"),
			"contentType":  info.ContentType,
			"fileType":     previewInfo.FileType.String(),
			"canPreview":   !previewInfo.ShouldDownload,
		},
	}

	c.JSON(http.StatusOK, response)
}
