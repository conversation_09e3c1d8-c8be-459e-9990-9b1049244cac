# Makefile for File Preview Service

# 应用配置
APP_NAME=file-preview-service
VERSION=1.0.0
BUILD_DIR=build

# Go 配置
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod

# 构建标记
BUILD_FLAGS=-ldflags="-s -w -X main.version=$(VERSION)"

.PHONY: all build clean test deps run help

# 默认目标
all: clean deps build

# 构建应用
build:
	@echo "构建应用程序..."
	@mkdir -p $(BUILD_DIR)
	$(GOBUILD) $(BUILD_FLAGS) -o $(BUILD_DIR)/$(APP_NAME) .
	@echo "构建完成: $(BUILD_DIR)/$(APP_NAME)"

# 构建多平台版本
build-all: clean deps
	@echo "构建多平台版本..."
	@mkdir -p $(BUILD_DIR)
	# Linux amd64
	GOOS=linux GOARCH=amd64 $(GOBUILD) $(BUILD_FLAGS) -o $(BUILD_DIR)/$(APP_NAME)-linux-amd64 .
	# Linux arm64
	GOOS=linux GOARCH=arm64 $(GOBUILD) $(BUILD_FLAGS) -o $(BUILD_DIR)/$(APP_NAME)-linux-arm64 .
	# Windows amd64
	GOOS=windows GOARCH=amd64 $(GOBUILD) $(BUILD_FLAGS) -o $(BUILD_DIR)/$(APP_NAME)-windows-amd64.exe .
	# macOS amd64
	GOOS=darwin GOARCH=amd64 $(GOBUILD) $(BUILD_FLAGS) -o $(BUILD_DIR)/$(APP_NAME)-darwin-amd64 .
	# macOS arm64
	GOOS=darwin GOARCH=arm64 $(GOBUILD) $(BUILD_FLAGS) -o $(BUILD_DIR)/$(APP_NAME)-darwin-arm64 .
	@echo "多平台构建完成"

# 安装依赖
deps:
	@echo "安装依赖..."
	$(GOMOD) download
	$(GOMOD) tidy

# 运行应用
run:
	@echo "启动应用..."
	$(GOCMD) run .

# 运行测试
test:
	@echo "运行测试..."
	$(GOTEST) -v ./...

# 清理构建文件
clean:
	@echo "清理构建文件..."
	$(GOCLEAN)
	@rm -rf $(BUILD_DIR)

# 格式化代码
fmt:
	@echo "格式化代码..."
	$(GOCMD) fmt ./...

# 代码检查
vet:
	@echo "代码检查..."
	$(GOCMD) vet ./...

# 安装应用到系统
install: build
	@echo "安装应用到系统..."
	@cp $(BUILD_DIR)/$(APP_NAME) /usr/local/bin/



# 显示帮助
help:
	@echo "可用命令:"
	@echo "  build      - 构建应用程序"
	@echo "  build-all  - 构建多平台版本"
	@echo "  deps       - 安装依赖"
	@echo "  run        - 运行应用程序"
	@echo "  test       - 运行测试"
	@echo "  clean      - 清理构建文件"
	@echo "  fmt        - 格式化代码"
	@echo "  vet        - 代码检查"
	@echo "  install    - 安装应用到系统"
	@echo "  help       - 显示此帮助信息"
