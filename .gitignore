# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Go workspace file
go.work

# Build directory
build/
dist/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Log files
*.log

# Environment files
.env
.env.local
.env.production

# Temporary files
tmp/
temp/

# Coverage reports
coverage.txt
coverage.html

# Vendor directory (if using dep)
vendor/

# Local configuration
config.local.yaml
config.local.yml

# Runtime files
*.pid
