package config

import (
	"fmt"
	"os"
)

// Config 应用程序配置
type Config struct {
	Server ServerConfig `yaml:"server"`
	Store  StoreConfig  `yaml:"store"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port int    `yaml:"port"`
	Host string `yaml:"host"`
}

// StoreConfig MinIO存储配置
type StoreConfig struct {
	Endpoint   string `yaml:"endpoint"`
	BucketName string `yaml:"bucketName"`
	AccessID   string `yaml:"accessId"`
	AccessKey  string `yaml:"accessKey"`
}

// Load 加载配置
func Load() *Config {
	// 使用环境变量或默认配置
	config := &Config{
		Server: ServerConfig{
			Port: getEnvInt("SERVER_PORT", 8080),
			Host: getEnvString("SERVER_HOST", "0.0.0.0"),
		},
		Store: StoreConfig{
			Endpoint:   getEnvString("MINIO_ENDPOINT", "http://************:9006"),
			BucketName: getEnvString("MINIO_BUCKET", "difyai"),
			AccessID:   getEnvString("MINIO_ACCESS_ID", "admin"),
			AccessKey:  getEnvString("MINIO_ACCESS_KEY", "admin1234"),
		},
	}

	return config
}

// getEnvString 获取环境变量字符串值
func getEnvString(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvInt 获取环境变量整数值
func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		var intValue int
		if _, err := fmt.Sscanf(value, "%d", &intValue); err == nil {
			return intValue
		}
	}
	return defaultValue
}
