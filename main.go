package main

import (
	"fmt"
	"log"
	"net/http"

	"file-preview-service/config"
	"file-preview-service/handler"
	"file-preview-service/service"
	"file-preview-service/storage"

	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	cfg := config.Load()
	log.Printf("启动文件预览服务...")
	log.Printf("MinIO配置: %s, Bucket: %s", cfg.Store.Endpoint, cfg.Store.BucketName)

	// 初始化MinIO客户端
	minioClient, err := storage.NewMinIOClient(&cfg.Store)
	if err != nil {
		log.Fatalf("初始化MinIO客户端失败: %v", err)
	}
	log.Printf("MinIO客户端初始化成功")

	// 初始化服务
	fileService := service.NewFileService(minioClient)

	// 创建路由器
	router := gin.New()

	// 设置文件上传限制
	router.MaxMultipartMemory = 8 << 20 // 8 MiB

	// 初始化处理器
	fileHandler := handler.NewFileHandler(fileService, minioClient)
	uploadHandler := handler.NewUploadHandler(minioClient.GetClient(), cfg.Store.BucketName)

	// 设置Gin模式
	gin.SetMode(gin.ReleaseMode)

	// 添加中间件
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(corsMiddleware())

	// 静态文件服务
	router.Static("/static", "./static")

	// 首页路由
	router.GET("/", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "/static/index.html")
	})

	// API路由组
	api := router.Group("/api")
	{
		// 文件预览接口
		api.GET("/preview/:filename", fileHandler.PreviewFile)

		// 文件列表接口
		api.GET("/files", fileHandler.ListFiles)

		// 文件信息接口
		api.GET("/info/:filename", fileHandler.GetFileInfo)

		// 文件上传接口
		api.POST("/upload", uploadHandler.UploadFile)
	}

	// 健康检查接口
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "ok",
			"service": "file-preview-service",
			"version": "1.0.0",
		})
	})

	// 启动服务器
	addr := fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port)
	log.Printf("服务器启动在: http://%s", addr)
	log.Printf("访问页面: http://%s", addr)

	if err := router.Run(addr); err != nil {
		log.Fatalf("启动服务器失败: %v", err)
	}
}

// corsMiddleware CORS中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.Header("Access-Control-Expose-Headers", "Content-Length, Content-Disposition")
		c.Header("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}
