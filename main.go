package main

import (
	"fmt"
	"log"
	"net/http"

	"file-preview/config"
	"file-preview/handler"
	"file-preview/service"

	"github.com/gin-gonic/gin"
)

func main() {
	cfg := config.Load()
	log.Printf("启动文件预览服务...")
	log.Printf("MinIO配置: %s, Bucket: %s", cfg.Store.Endpoint, cfg.Store.BucketName)

	minioClient, err := service.NewMinIOClient(&cfg.Store)
	if err != nil {
		log.Fatalf("初始化MinIO客户端失败: %v", err)
	}
	log.Printf("MinIO客户端初始化成功")

	gin.SetMode(gin.ReleaseMode)

	fileService := service.NewFileService(minioClient)
	router := gin.New()
	router.MaxMultipartMemory = 8 << 20 // 8 MiB

	fileHandler := handler.NewFileHandler(fileService, minioClient)
	uploadHandler := handler.NewUploadHandler(minioClient.GetClient(), cfg.Store.BucketName)

	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(corsMiddleware())

	router.GET("/", func(c *gin.Context) {
		c.Redirect(http.StatusFound, "/static/")
	})
	router.Static("/static", "./static")

	api := router.Group("/api")
	{
		api.GET("/preview/:filename", fileHandler.PreviewFile)
		api.GET("/files", fileHandler.ListFiles)
		api.GET("/info/:filename", fileHandler.GetFileInfo)
		api.POST("/upload", uploadHandler.UploadFile)
	}

	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "ok",
			"service": "file-preview",
			"version": "1.0.0",
		})
	})

	addr := fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port)
	log.Printf("服务器启动在: http://%s", addr)

	if err := router.Run(addr); err != nil {
		log.Fatalf("启动服务器失败: %v", err)
	}
}

// corsMiddleware CORS中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.Header("Access-Control-Expose-Headers", "Content-Length, Content-Disposition")
		c.Header("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}
