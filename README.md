# 文件智能预览服务

一个基于Golang的文件智能预览Web服务，支持图片、PDF、文本文件在线预览，其他文件自动下载。

## 🎯 功能特性

- **智能预览**: 根据文件类型自动选择预览方式
- **支持格式**: 
  - 图片文件 (PNG, JPG, GIF, BMP, WebP, SVG) - 网页内直接展示
  - PDF文件 - 浏览器插件内嵌显示
  - 文本文件 (TXT, LOG, MD, JSON, XML, CSV, YAML) - 文本区域展示
  - 其他格式 - 自动触发下载
- **MinIO集成**: 安全地从MinIO对象存储获取文件
- **现代UI**: 响应式设计，支持移动端访问
- **高性能**: 基于Gin框架，单体部署

## 🛠️ 技术栈

- **后端**: Go 1.21 + Gin Framework
- **存储**: MinIO Object Storage
- **前端**: 原生HTML/CSS/JavaScript
- **部署**: 单体二进制文件

## 📦 快速开始

### 环境要求

- Go 1.21 或更高版本
- MinIO服务（可访问）

### 安装和运行

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd file-preview-service
   ```

2. **安装依赖**
   ```bash
   go mod download
   ```

3. **配置环境变量**（可选）
   ```bash
   export MINIO_ENDPOINT="http://************:9006"
   export MINIO_BUCKET="difyai"
   export MINIO_ACCESS_ID="admin"
   export MINIO_ACCESS_KEY="admin1234"
   export SERVER_PORT="8080"
   export SERVER_HOST="0.0.0.0"
   ```

4. **运行服务**
   ```bash
   go run .
   ```
   
   或使用Makefile:
   ```bash
   make run
   ```

5. **访问服务**
   打开浏览器访问: http://localhost:8080

### 使用Makefile构建

```bash
make build    # 构建应用
make run      # 运行应用
make help     # 查看所有命令
```

## 🔧 配置说明

### 默认配置

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| SERVER_HOST | 0.0.0.0 | 服务器监听地址 |
| SERVER_PORT | 8080 | 服务器端口 |
| MINIO_ENDPOINT | http://************:9006 | MinIO服务地址 |
| MINIO_BUCKET | difyai | MinIO存储桶名 |
| MINIO_ACCESS_ID | admin | MinIO访问ID |
| MINIO_ACCESS_KEY | admin1234 | MinIO访问密钥 |

### 自定义配置

通过环境变量覆盖默认配置，或直接修改 `config/config.go` 文件。

## 📡 API 接口

### 1. 文件预览
```
GET /api/preview/{filename}
```
根据文件类型返回预览内容或触发下载。

### 2. 文件列表
```
GET /api/files?prefix={prefix}
```
获取存储桶中的文件列表。

### 3. 文件信息
```
GET /api/info/{filename}
```
获取指定文件的详细信息。

### 4. 文件上传
```
POST /api/upload
```
上传文件到存储桶。

### 5. 健康检查
```
GET /health
```
服务健康状态检查。

## 🖥️ 界面功能

### 主要功能
- **文件列表**: 显示所有可用文件，包含文件信息
- **智能预览**: 点击预览按钮查看文件内容
- **文件下载**: 支持所有文件的下载功能
- **响应式设计**: 适配桌面和移动设备

### 文件类型处理

| 文件类型 | 图标 | 预览方式 | Content-Type |
|----------|------|----------|--------------|
| 图片文件 | 🖼️ | 内嵌显示 | image/* |
| PDF文件 | 📄 | iframe内嵌 | application/pdf |
| 文本文件 | 📝 | 代码高亮 | text/plain |
| 其他文件 | 📁 | 自动下载 | application/octet-stream |

## 🚀 部署

### 二进制部署

1. 构建应用:
   ```bash
   make build
   ```

2. 部署文件:
   ```bash
   # 复制二进制文件
   cp build/file-preview-service /usr/local/bin/
   
   # 复制静态文件
   cp -r static /opt/file-preview-service/
   ```

3. 创建系统服务 (systemd):
   ```ini
   [Unit]
   Description=File Preview Service
   After=network.target

   [Service]
   Type=simple
   User=nobody
   WorkingDirectory=/opt/file-preview-service
   ExecStart=/usr/local/bin/file-preview-service
   Restart=always

   [Install]
   WantedBy=multi-user.target
   ```



## 🔒 安全考虑

- 所有文件访问通过后端代理，前端不直连MinIO
- 支持CORS配置，可限制访问来源
- 文件名进行URL编码处理，防止路径遍历
- 文件类型基于扩展名和MIME类型双重验证

## 📝 开发说明

### 项目结构
```
file-preview-service/
├── config/          # 配置管理
├── handler/         # HTTP处理器
├── service/         # 业务逻辑层
├── storage/         # 存储层封装
├── static/          # 静态文件
├── main.go          # 主程序入口
├── go.mod           # Go模块文件
├── Makefile         # 构建脚本
└── README.md        # 项目文档
```

### 扩展功能
- 添加新的文件类型支持，修改 `service/file_service.go`
- 自定义预览样式，修改 `static/index.html`
- 添加认证功能，在 `main.go` 中增加中间件

## 🐛 故障排除

### 常见问题

1. **无法连接MinIO** - 检查MinIO服务状态和网络连接
2. **文件预览失败** - 检查文件是否存在于存储桶中
3. **端口被占用** - 修改 `SERVER_PORT` 环境变量

## 📄 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。

## 📞 支持

如有问题或建议，请创建 Issue 或联系开发团队。
